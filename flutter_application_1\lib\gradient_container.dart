import 'package:flutter/material.dart';

const startAlignment = Alignment.centerRight;
const endAlignment = Alignment.topLeft;

//make a costum widged
class Gradientcontainer extends StatefulWidget {
  //constrator functoin extends StatelessWidget {
  const Gradientcontainer(this.color1, this.color2, {super.key});
  final Color color1;
  final Color color2;

  @override
  State<Gradientcontainer> createState() => _GradientcontainerState();
}

class _GradientcontainerState extends State<Gradientcontainer> {
  String activediceimage = 'assets/images/dice-1.png';

  void rolldice() {
    setState(() {
      activediceimage = 'assets/images/dice-2.png';
    });
  }

  @override
  Widget build(context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [widget.color1, widget.color2],
          begin: startAlignment,
          end: endAlignment,
        ),
      ),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(activediceimage, width: 200, height: 200),
            const SizedBox(height: 20, width: 20),
            TextButton(
              onPressed: rolldice,
              style: TextButton.styleFrom(
                foregroundColor: Color.fromARGB(255, 0, 226, 234),
                textStyle: TextStyle(fontSize: 28),
              ),
              child: Text("role dice"),
            ),
          ],
        ),
      ),
    );
  }
}
